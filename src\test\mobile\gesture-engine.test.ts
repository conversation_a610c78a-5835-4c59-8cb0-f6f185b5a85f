import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { GestureEngine, type GestureCallbacks, type GestureConfig } from '@/lib/mobile/gesture-engine';

// Mock DOM APIs
const mockVibrate = vi.fn();
Object.defineProperty(global, 'navigator', {
  value: {
    vibrate: mockVibrate,
    maxTouchPoints: 10,
  },
  writable: true,
});

// Mock Touch interface
class MockTouch implements Touch {
  identifier: number;
  target: EventTarget;
  screenX: number;
  screenY: number;
  clientX: number;
  clientY: number;
  pageX: number;
  pageY: number;
  radiusX: number = 1;
  radiusY: number = 1;
  rotationAngle: number = 0;
  force: number = 1;

  constructor(
    identifier: number,
    clientX: number,
    clientY: number,
    target: EventTarget = document.body
  ) {
    this.identifier = identifier;
    this.clientX = clientX;
    this.clientY = clientY;
    this.pageX = clientX;
    this.pageY = clientY;
    this.screenX = clientX;
    this.screenY = clientY;
    this.target = target;
  }
}

// Mock TouchEvent
class MockTouchEvent extends Event implements TouchEvent {
  touches: TouchList;
  targetTouches: TouchList;
  changedTouches: TouchList;
  altKey: boolean = false;
  metaKey: boolean = false;
  ctrlKey: boolean = false;
  shiftKey: boolean = false;

  constructor(type: string, touches: Touch[]) {
    super(type, { bubbles: true, cancelable: true });
    this.touches = touches as any;
    this.targetTouches = touches as any;
    this.changedTouches = touches as any;
  }
}

describe('GestureEngine', () => {
  let element: HTMLElement;
  let gestureEngine: GestureEngine;
  let callbacks: GestureCallbacks;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Create test element
    element = document.createElement('div');
    element.style.width = '400px';
    element.style.height = '300px';
    document.body.appendChild(element);

    // Mock getBoundingClientRect
    element.getBoundingClientRect = vi.fn(() => ({
      left: 0,
      top: 0,
      right: 400,
      bottom: 300,
      width: 400,
      height: 300,
      x: 0,
      y: 0,
      toJSON: () => {},
    }));

    // Setup callbacks
    callbacks = {
      onTap: vi.fn(),
      onDoubleTap: vi.fn(),
      onLongPress: vi.fn(),
      onSwipe: vi.fn(),
      onPan: vi.fn(),
      onPinch: vi.fn(),
      onRotate: vi.fn(),
      onTwoFingerTap: vi.fn(),
      onThreeFingerTap: vi.fn(),
      onEdgeSwipe: vi.fn(),
      onForceTouch: vi.fn(),
      onGestureStart: vi.fn(),
      onGestureEnd: vi.fn(),
    };
  });

  afterEach(() => {
    gestureEngine?.destroy();
    document.body.removeChild(element);
  });

  describe('Initialization', () => {
    it('should initialize with default config', () => {
      gestureEngine = new GestureEngine(element, callbacks);
      expect(gestureEngine).toBeDefined();
    });

    it('should initialize with custom config', () => {
      const customConfig: Partial<GestureConfig> = {
        tapTimeout: 500,
        longPressTimeout: 1000,
        enableHapticFeedback: false,
      };

      gestureEngine = new GestureEngine(element, callbacks, customConfig);
      expect(gestureEngine).toBeDefined();
    });

    it('should bind event listeners to element', () => {
      const addEventListenerSpy = vi.spyOn(element, 'addEventListener');
      gestureEngine = new GestureEngine(element, callbacks);

      expect(addEventListenerSpy).toHaveBeenCalledWith('touchstart', expect.any(Function), { passive: false });
      expect(addEventListenerSpy).toHaveBeenCalledWith('touchmove', expect.any(Function), { passive: false });
      expect(addEventListenerSpy).toHaveBeenCalledWith('touchend', expect.any(Function), { passive: false });
      expect(addEventListenerSpy).toHaveBeenCalledWith('touchcancel', expect.any(Function), { passive: false });
    });
  });

  describe('Tap Gestures', () => {
    beforeEach(() => {
      gestureEngine = new GestureEngine(element, callbacks);
    });

    it('should detect single tap', async () => {
      const touch = new MockTouch(1, 100, 100, element);

      // Touch start
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));
      expect(callbacks.onGestureStart).toHaveBeenCalledTimes(1);

      // Touch end (short duration)
      await new Promise(resolve => setTimeout(resolve, 50));
      element.dispatchEvent(new MockTouchEvent('touchend', []));
      expect(callbacks.onGestureEnd).toHaveBeenCalledTimes(1);

      // Wait for tap timeout
      await new Promise(resolve => setTimeout(resolve, 350));

      expect(callbacks.onTap).toHaveBeenCalledTimes(1);
    });

    it('should detect double tap', async () => {
      const touch = new MockTouch(1, 100, 100, element);
      
      // First tap
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));
      await new Promise(resolve => setTimeout(resolve, 50));
      element.dispatchEvent(new MockTouchEvent('touchend', []));

      // Second tap (within double tap timeout)
      await new Promise(resolve => setTimeout(resolve, 100));
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));
      await new Promise(resolve => setTimeout(resolve, 50));
      element.dispatchEvent(new MockTouchEvent('touchend', []));

      expect(callbacks.onDoubleTap).toHaveBeenCalledTimes(1);
      expect(callbacks.onTap).not.toHaveBeenCalled();
    });

    it('should detect long press', async () => {
      const touch = new MockTouch(1, 100, 100, element);
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));
      
      // Wait for long press timeout
      await new Promise(resolve => setTimeout(resolve, 600));

      expect(callbacks.onLongPress).toHaveBeenCalledTimes(1);
    });

    it('should cancel tap on movement', () => {
      const touch1 = new MockTouch(1, 100, 100, element);
      const touch2 = new MockTouch(1, 150, 150, element); // Moved too far
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1]));
      element.dispatchEvent(new MockTouchEvent('touchmove', [touch2]));
      element.dispatchEvent(new MockTouchEvent('touchend', []));

      expect(callbacks.onTap).not.toHaveBeenCalled();
    });
  });

  describe('Multi-finger Gestures', () => {
    beforeEach(() => {
      gestureEngine = new GestureEngine(element, callbacks);
    });

    it('should detect two-finger tap', () => {
      const touch1 = new MockTouch(1, 100, 100, element);
      const touch2 = new MockTouch(2, 200, 200, element);
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1, touch2]));

      expect(callbacks.onTwoFingerTap).toHaveBeenCalledTimes(1);
    });

    it('should detect three-finger tap', () => {
      const touch1 = new MockTouch(1, 100, 100, element);
      const touch2 = new MockTouch(2, 200, 200, element);
      const touch3 = new MockTouch(3, 300, 300, element);
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1, touch2, touch3]));

      expect(callbacks.onThreeFingerTap).toHaveBeenCalledTimes(1);
    });

    it('should detect pinch gesture', () => {
      const touch1 = new MockTouch(1, 100, 100, element);
      const touch2 = new MockTouch(2, 200, 200, element);
      
      // Start with two fingers
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1, touch2]));

      // Move fingers closer together (pinch in)
      const touch1Moved = new MockTouch(1, 120, 120, element);
      const touch2Moved = new MockTouch(2, 180, 180, element);
      element.dispatchEvent(new MockTouchEvent('touchmove', [touch1Moved, touch2Moved]));

      expect(callbacks.onPinch).toHaveBeenCalled();
    });

    it('should detect rotation gesture', () => {
      const touch1 = new MockTouch(1, 100, 100, element);
      const touch2 = new MockTouch(2, 200, 100, element); // Horizontal line
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1, touch2]));

      // Rotate to vertical line
      const touch1Moved = new MockTouch(1, 150, 50, element);
      const touch2Moved = new MockTouch(2, 150, 150, element);
      element.dispatchEvent(new MockTouchEvent('touchmove', [touch1Moved, touch2Moved]));

      expect(callbacks.onRotate).toHaveBeenCalled();
    });
  });

  describe('Swipe Gestures', () => {
    beforeEach(() => {
      gestureEngine = new GestureEngine(element, callbacks);
    });

    it('should detect horizontal swipe', async () => {
      const touch1 = new MockTouch(1, 50, 150, element);
      const touch2 = new MockTouch(1, 250, 150, element); // Swipe right

      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1]));

      // Quick movement
      await new Promise(resolve => setTimeout(resolve, 50));
      element.dispatchEvent(new MockTouchEvent('touchmove', [touch2]));

      await new Promise(resolve => setTimeout(resolve, 50));
      element.dispatchEvent(new MockTouchEvent('touchend', []));

      expect(callbacks.onSwipe).toHaveBeenCalledTimes(1);

      const swipeEvent = callbacks.onSwipe.mock.calls[0][0];
      expect(Math.abs(swipeEvent.deltaX)).toBeGreaterThan(50); // Significant horizontal movement
    });

    it('should detect vertical swipe', async () => {
      const touch1 = new MockTouch(1, 200, 50, element);
      const touch2 = new MockTouch(1, 200, 250, element); // Swipe down

      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1]));

      await new Promise(resolve => setTimeout(resolve, 50));
      element.dispatchEvent(new MockTouchEvent('touchmove', [touch2]));

      await new Promise(resolve => setTimeout(resolve, 50));
      element.dispatchEvent(new MockTouchEvent('touchend', []));

      expect(callbacks.onSwipe).toHaveBeenCalledTimes(1);

      const swipeEvent = callbacks.onSwipe.mock.calls[0][0];
      expect(Math.abs(swipeEvent.deltaY)).toBeGreaterThan(50); // Significant vertical movement
    });

    it('should not detect swipe if movement is too slow', async () => {
      const touch1 = new MockTouch(1, 50, 150, element);
      const touch2 = new MockTouch(1, 250, 150, element);
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1]));
      
      // Slow movement
      await new Promise(resolve => setTimeout(resolve, 400));
      element.dispatchEvent(new MockTouchEvent('touchmove', [touch2]));
      element.dispatchEvent(new MockTouchEvent('touchend', []));

      expect(callbacks.onSwipe).not.toHaveBeenCalled();
    });
  });

  describe('Pan Gestures', () => {
    beforeEach(() => {
      gestureEngine = new GestureEngine(element, callbacks);
    });

    it('should detect pan gesture', () => {
      const touch1 = new MockTouch(1, 100, 100, element);
      const touch2 = new MockTouch(1, 120, 120, element);
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1]));
      element.dispatchEvent(new MockTouchEvent('touchmove', [touch2]));

      expect(callbacks.onPan).toHaveBeenCalled();
      
      const panEvent = callbacks.onPan.mock.calls[0][0];
      expect(panEvent.deltaX).toBe(20);
      expect(panEvent.deltaY).toBe(20);
    });

    it('should not detect pan for small movements', () => {
      const touch1 = new MockTouch(1, 100, 100, element);
      const touch2 = new MockTouch(1, 105, 105, element); // Small movement
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch1]));
      element.dispatchEvent(new MockTouchEvent('touchmove', [touch2]));

      expect(callbacks.onPan).not.toHaveBeenCalled();
    });
  });

  describe('Edge Gestures', () => {
    beforeEach(() => {
      gestureEngine = new GestureEngine(element, callbacks, {
        edgeThreshold: 20,
      });
    });

    it('should detect edge swipe from left', () => {
      const touch = new MockTouch(1, 10, 150, element); // Near left edge
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));

      expect(callbacks.onGestureStart).toHaveBeenCalled();
      
      const gestureEvent = callbacks.onGestureStart.mock.calls[0][0];
      expect(gestureEvent.type).toBe('tap'); // Initial gesture type
    });

    it('should detect edge swipe from right', () => {
      const touch = new MockTouch(1, 390, 150, element); // Near right edge
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));

      expect(callbacks.onGestureStart).toHaveBeenCalled();
    });
  });

  describe('Configuration Updates', () => {
    beforeEach(() => {
      gestureEngine = new GestureEngine(element, callbacks);
    });

    it('should update configuration', () => {
      const newConfig = {
        tapTimeout: 500,
        enableHapticFeedback: false,
      };

      gestureEngine.updateConfig(newConfig);
      
      // Configuration should be updated (we can't directly test private properties)
      expect(gestureEngine).toBeDefined();
    });

    it('should update callbacks', () => {
      const newCallbacks = {
        onTap: vi.fn(),
      };

      gestureEngine.updateCallbacks(newCallbacks);
      
      // Test that new callback is used
      const touch = new MockTouch(1, 100, 100, element);
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));
      element.dispatchEvent(new MockTouchEvent('touchend', []));

      // Original callback should not be called, new one should be
      expect(callbacks.onTap).not.toHaveBeenCalled();
    });
  });

  describe('Haptic Feedback', () => {
    beforeEach(() => {
      gestureEngine = new GestureEngine(element, callbacks, {
        enableHapticFeedback: true,
      });
    });

    it('should trigger haptic feedback on tap', async () => {
      const touch = new MockTouch(1, 100, 100, element);
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));
      await new Promise(resolve => setTimeout(resolve, 50));
      element.dispatchEvent(new MockTouchEvent('touchend', []));

      // Wait for tap to be processed
      await new Promise(resolve => setTimeout(resolve, 350));

      expect(mockVibrate).toHaveBeenCalledWith([10]);
    });

    it('should not trigger haptic feedback when disabled', async () => {
      gestureEngine.destroy();
      gestureEngine = new GestureEngine(element, callbacks, {
        enableHapticFeedback: false,
      });

      const touch = new MockTouch(1, 100, 100, element);
      
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));
      await new Promise(resolve => setTimeout(resolve, 50));
      element.dispatchEvent(new MockTouchEvent('touchend', []));

      await new Promise(resolve => setTimeout(resolve, 350));

      expect(mockVibrate).not.toHaveBeenCalled();
    });
  });

  describe('Cleanup', () => {
    it('should remove event listeners on destroy', () => {
      const removeEventListenerSpy = vi.spyOn(element, 'removeEventListener');
      gestureEngine = new GestureEngine(element, callbacks);
      
      gestureEngine.destroy();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('touchstart', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('touchmove', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('touchend', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('touchcancel', expect.any(Function));
    });

    it('should clear all timers on destroy', () => {
      gestureEngine = new GestureEngine(element, callbacks);
      
      // Start a long press
      const touch = new MockTouch(1, 100, 100, element);
      element.dispatchEvent(new MockTouchEvent('touchstart', [touch]));
      
      // Destroy before long press timeout
      gestureEngine.destroy();
      
      // Long press should not fire
      expect(callbacks.onLongPress).not.toHaveBeenCalled();
    });
  });
});
